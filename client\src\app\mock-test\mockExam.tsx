"use client";

import { But<PERSON> } from "@/components/ui/button";
import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getStudentDetail } from "@/services/studentDetailServiceApi";
import { saveMockExamResult, getMockExamResults } from "@/services/mock-exam-resultApi";
import { saveTerminatedStudent as saveTermination, countUwhizAttemp } from "@/services/uwhizMockExamTerminationApi";
import { Loader, Clock } from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import examLogo from '../../../public/uwhizExam.png';
import { uwhizMockQuestionForStudent } from "@/services/uwhizMockExamApi";
import { saveExamAttempt } from "./restictExamAttempt";
import { addUestCoinTranscation, updateUestCoins } from "@/services/uestCoinTransctionApi";
import { saveMockExamStreak, getMockExamStreak, getweeklyMockExamStreak, saveweeklyMockExamStreak} from "@/services/mockExamStreakApi";
import useStudentId from "@/hooks/getStudentId";
import { WhatsappShareButton, WhatsappIcon } from 'react-share';
import * as htmlToImage from 'html-to-image';

interface Question {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: string;
}

interface UserAnswer {
  questionId: string;
  selectedAnswer: string;
  isCorrect: boolean;
}

interface StreakData {
  streak: number;
  lastAttempt: string;
}

const QuizHeader = React.memo(
  () => {
    return (
      <header className="fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md">
        <div className="flex items-center justify-center gap-3">
          <Image
            height={60}
            width={60}
            src={examLogo.src}
            alt="Uwhiz Logo"
            quality={100}
            className="object-contain sm:h-20 sm:w-20"
          />
          <h1 className="text-lg sm:text-2xl font-bold tracking-tight">Uest Daily Quiz</h1>
        </div>
      </header>
    );
  }
);
QuizHeader.displayName = "QuizHeader";

export default function QuizPage() {
  const router = useRouter();
  const searchParams = useSearchParams(); 
  const studentId = useStudentId();
  const [isWeekly, setIsWeekly] = useState(false); 

  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isExamTakenDialogOpen, setIsExamTakenDialogOpen] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [showTermination, setShowTermination] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);
  const [isQuizCompleted, setIsQuizCompleted] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isApiCallPending, setIsApiCallPending] = useState(false);
  const tickSoundRef = useRef<HTMLAudioElement | null>(null);
  const [terminationMessage, setTerminationMessage] = useState<string>("");
  const [violationCount, setViolationCount] = useState(0);
  const [showAnswerFeedback, setShowAnswerFeedback] = useState(false);
  const [streakData, setStreakData] = useState<StreakData | null>(null);
  const [streakLoading, setStreakLoading] = useState(false);
  const [streakError, setStreakError] = useState<string | null>(null);
  const cardRef = useRef<HTMLDivElement | null>(null);
  const [isProfileNotApproved, setIsProfileNotApproved] = useState(false);

  useEffect(() => {
    const isWeeklyParam = searchParams.get("isWeekly");
    setIsWeekly(isWeeklyParam === "true");
  }, [searchParams]);

  const initializeViolationCounts = async () => {
    if (!studentId) {
      return 0;
    }
    try {
      const count = await countUwhizAttemp(studentId);
      return typeof count === 'number' ? count : 0;
    } catch (error) {
      console.error("Failed to fetch violation count:", error);
      return 0;
    }
  };

  useEffect(() => {
    const fetchCounts = async () => {
      const count = await initializeViolationCounts();
      setViolationCount(count);
    };
    fetchCounts();
  }, [studentId]);

  useEffect(() => {
    if (violationCount >= 3) {
      setShowTermination(true);
      setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
      if (studentId) {
        saveExamAttempt(studentId);
      }
    }
  }, [violationCount, studentId]);

  useEffect(() => {
    if (studentId) {
      const checkExamEligibility = async () => {
        try {
          const response = await getMockExamResults(studentId, 1, 1, { isWeekly });
          if (response.success && response.data.data.mockExamResults.length > 0) {
            const latestExam = response.data.data.mockExamResults[0];
            const examDate = new Date(latestExam.createdAt).toISOString().split('T')[0];
            const today = new Date().toISOString().split('T')[0];

            if (examDate === today && !isWeekly) {
              setIsExamTakenDialogOpen(true);
            } else if (isWeekly) {
              const startOfWeek = new Date();
              startOfWeek.setDate(startOfWeek.getDate() - (startOfWeek.getDay() === 0 ? 6 : startOfWeek.getDay() - 1));
              startOfWeek.setHours(0, 0, 0, 0);
              const hasAttemptedThisWeek = new Date(latestExam.createdAt) >= startOfWeek;
              if (hasAttemptedThisWeek) {
                setIsExamTakenDialogOpen(true);
              }
            }
          }
        } catch (error: any) {
          toast.error("Failed to verify exam eligibility.", error);
        }
      };
      checkExamEligibility();
    }
  }, [studentId, isWeekly]);

  useEffect(() => {
    tickSoundRef.current = new Audio("/clock-ticking-sound-effect.mp3");
    tickSoundRef.current.loop = true;
    return () => {
      if (tickSoundRef.current) {
        tickSoundRef.current.pause();
        tickSoundRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (
      questions.length > 0 &&
      timeLeft <= 5 &&
      timeLeft > 0 &&
      !isDialogOpen &&
      !showTermination &&
      !isQuizCompleted &&
      !isLoginDialogOpen &&
      !isProfileDialogOpen &&
      !isExamTakenDialogOpen &&
      tickSoundRef.current
    ) {
      tickSoundRef.current.play().catch((error) => {
        console.error("Failed to play tick sound:", error);
      });
    } else if (tickSoundRef.current) {
      tickSoundRef.current.pause();
    }
  }, [timeLeft, questions, isDialogOpen, showTermination, isQuizCompleted, isLoginDialogOpen, isProfileDialogOpen, isExamTakenDialogOpen]);

  const handleNextQuestion = useCallback(async () => {
    const currentQuestion = questions[currentQuestionIndex];
    if (selectedAnswer) {
      const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
      setUserAnswers((prev) => [
        ...prev,
        {
          questionId: currentQuestion.id,
          selectedAnswer,
          isCorrect,
        },
      ]);
      setShowAnswerFeedback(true);
      setTimeout(() => {
        setShowAnswerFeedback(false);
        setSelectedAnswer(null);
        if (currentQuestionIndex < questions.length - 1) {
          setCurrentQuestionIndex((prev) => prev + 1);
        } else {
          setIsQuizCompleted(true);
        }
      }, 1000);
    } else {
      setUserAnswers((prev) => [
        ...prev,
        {
          questionId: currentQuestion.id,
          selectedAnswer: "skipped",
          isCorrect: false,
        },
      ]);
      toast.warning("Question skipped.");
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex((prev) => prev + 1);
      } else {
        setIsQuizCompleted(true);
      }
    }
  }, [selectedAnswer, questions, currentQuestionIndex]);

  const calculateScore = useMemo(() => {
    return userAnswers.reduce((score, answer) => score + (answer.isCorrect ? 1 : 0), 0);
  }, [userAnswers]);

  const calculateCoins = useMemo(() => {
    const percentage = (calculateScore / questions.length) * 100;
    let baseCoins;
    if (percentage >= 100) baseCoins = 5;
    else if (percentage >= 90) baseCoins = 4;
    else if (percentage >= 80) baseCoins = 3;
    else if (percentage >= 70) baseCoins = 2;
    else if (percentage >= 60) baseCoins = 1;
    else baseCoins = 0;
    return isWeekly ? baseCoins * 5 : baseCoins;
  }, [calculateScore, questions.length, isWeekly]);

  useEffect(() => {
    if (isQuizCompleted && studentId) {
      const saveResultAndCoins = async () => {
        try {
          const streakResponse = isWeekly
            ? await saveweeklyMockExamStreak(studentId)
            : await saveMockExamStreak(studentId);
          if (!streakResponse.success) {
            toast.error(streakResponse.error);
            setStreakError(streakResponse.error);
            return;
          }
          setStreakLoading(true);
          const streakFetchResponse = isWeekly
            ? await getweeklyMockExamStreak(studentId)
            : await getMockExamStreak(studentId);
          setStreakLoading(false);
          if (streakFetchResponse.success) {
            setStreakData(streakFetchResponse.data);
            toast.success(`Streak updated successfully! Current streak: ${streakFetchResponse.data.streak} ${isWeekly ? 'Weeks' : 'Days'}`);
          } else {
            setStreakError(streakFetchResponse.error);
            toast.error(streakFetchResponse.error);
          }

          const streakCoins = 1; 
          const totalCoins = calculateCoins + streakCoins;

          const resultData = {
            studentId,
            score: calculateScore,
            coinEarnings: totalCoins,
            isWeekly,
          };
          const response = await saveMockExamResult(resultData);
          if (response.success) {
            toast.success("Result saved successfully!");
          } else {
            toast.error(response.error);
          }

          const coinsData = {
            modelId: studentId,
            modelType: "STUDENT",
            coins: totalCoins,
          };
          const updateCoinsResponse = await updateUestCoins(coinsData);
          if (updateCoinsResponse.success) {
            toast.success(`Coins updated successfully! Added ${totalCoins} coins (Score: ${calculateCoins}, Streak: ${streakCoins}).`);
          } else {
            toast.error(updateCoinsResponse.error);
          }

          const transactionData = {
            modelId: studentId,
            modelType: "STUDENT",
            amount: totalCoins,
            type: "CREDIT",
            reason: `${isWeekly ? 'Weekly' : 'Daily'} Quiz Exam (Score + Streak: ${streakCoins})`,
          };
          const addTransactionResponse = await addUestCoinTranscation(transactionData);
          if (addTransactionResponse.success) {
            toast.success("Transaction logged successfully!");
          } else {
            toast.error(addTransactionResponse.error);
          }

          await saveExamAttempt(studentId);
          await exitFullScreen();
        } catch (error: any) {
          setStreakLoading(false);
          setStreakError(`Failed to fetch streak: ${error.message}`);
          toast.error(`Failed to save result, update coins, or update streak: ${error.message}`);
        }
      };
      saveResultAndCoins();
    }
  }, [isQuizCompleted, studentId, calculateScore, calculateCoins, isWeekly]);

  useEffect(() => {
    if (questions.length > 0 && timeLeft > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          const newTime = prev - 1;
          if (newTime <= 0) {
            clearInterval(timer);
            handleNextQuestion();
            return 0;
          }
          return newTime;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [timeLeft, questions, currentQuestionIndex, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, isExamTakenDialogOpen, handleNextQuestion]);

  useEffect(() => {
    if (questions.length > 0 && !isDialogOpen && !showTermination && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen) {
      const newTime = 45;
      setTimeLeft(newTime);
    }
  }, [currentQuestionIndex, questions, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, isExamTakenDialogOpen]);

  useEffect(() => {
    if (isQuizCompleted && typeof window !== 'undefined') {
      import('canvas-confetti').then((module) => {
        const confetti = module.default;
        confetti({
          particleCount: 100,
          spread: 80,
          startVelocity: 30,
          ticks: 200,
          origin: { x: 0.5, y: 0.2 },
          colors: ['#FF4500', '#FFD700', '#FF69B4', '#00FF00', '#1E90FF'],
          shapes: ['square'],
          gravity: 0.3,
          scalar: 1.2,
        });
      });
    }
  }, [isQuizCompleted]);

  const fetchQuizState = useCallback(async () => {
    if (!studentId) {
      setIsLoginDialogOpen(true);
      return;
    }

    try {
      const studentResponse = await getStudentDetail(studentId);
      if (!studentResponse.success) {
        toast.error(studentResponse.error);
        setIsProfileDialogOpen(true);
        return;
      }
      if (studentResponse.data.status != "APPROVED") {
        setIsProfileDialogOpen(true);
        setIsProfileNotApproved(true);
        toast.error("Student Profile is not approved yet. Please check your profile.");
        return;
      }
      const medium = studentResponse.data.medium.toUpperCase();

      const questionResponse = await uwhizMockQuestionForStudent(studentId, medium,isWeekly);
      if (questionResponse && Array.isArray(questionResponse)) {
        setQuestions(questionResponse);
        setTimeLeft(45);
        setIsDialogOpen(true);
      } else {
        toast.error("No questions found or invalid response.");
      }
    } catch (error: any) {
      toast.error(error);
    }
  }, [studentId, isWeekly]);

  useEffect(() => {
    if (studentId && !isExamTakenDialogOpen) {
      fetchQuizState();
    }
  }, [studentId, fetchQuizState, isExamTakenDialogOpen]);

  const exitFullScreen = async (maxAttempts = 3, attempt = 1): Promise<boolean> => {
    try {
      if (document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement) {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
          await (document as any).mozCancelFullScreen();
        }
        await new Promise(resolve => setTimeout(resolve, 100));
        if (!document.fullscreenElement && !(document as any).webkitFullscreenElement && !(document as any).mozFullScreenElement) {
          return true;
        }
        if (attempt < maxAttempts) {
          return await exitFullScreen(maxAttempts, attempt + 1);
        }
        throw new Error("Max attempts reached");
      }
      return true;
    } catch (err: unknown) {
      console.error(`Failed to exit full-screen mode (attempt ${attempt}):`, err);
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return await exitFullScreen(maxAttempts, attempt + 1);
      }
      toast.error("Failed to exit full-screen mode. Please press Esc to exit manually.");
      return false;
    }
  };

  useEffect(() => {
    if (isQuizCompleted && studentId && showTermination) {
      saveExamAttempt(studentId);
      exitFullScreen();
    }
  }, [isQuizCompleted, studentId]);

  useEffect(() => {
    if (showTermination && studentId) {
      exitFullScreen().then((success) => {
        console.log("Quiz terminated and full-screen exited.", success);
      });
    }
  }, [showTermination, studentId]);

  const enterFullScreen = () => {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch((err: unknown) => console.error("Failed to enter fullscreen:", err));
    }
  };

  const handleStartQuiz = () => {
    setIsDialogOpen(false);
    enterFullScreen();
    if (questions.length > 0) {
      setTimeLeft(45);
    }
  };

  const handleGoToProfile = () => {
    router.push("/student/profile");
  };

  const handleCloseExamTakenDialog = () => {
    setIsExamTakenDialogOpen(false);
    router.push("/mock-exam-card");
  };

  const handleKeyDown = useCallback(
    async (event: KeyboardEvent) => {
      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;

      const restrictedKeys = ["Alt", "Control", "Tab", "Shift", "Enter"];
      const functionKeys = ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"];
      const isDevToolsShortcut =
        (event.ctrlKey && event.shiftKey && (event.key === "I" || event.key === "J" || event.key === "C")) ||
        (event.metaKey && event.altKey && event.key === "I") ||
        event.key === "F12";
      const isCopyShortcut =
        (event.ctrlKey || event.metaKey) && (event.key === "c" || event.key === "C");

      if (
        restrictedKeys.includes(event.key) ||
        functionKeys.includes(event.key) ||
        isDevToolsShortcut ||
        isCopyShortcut
      ) {
        event.preventDefault();
        if (isCopyShortcut) {
          toast.warning("Copying is disabled during the quiz.");
          return;
        }
        if (!studentId) {
          setViolationCount(0);
          return;
        }
        setIsApiCallPending(true);
        try {
          const violationType = isDevToolsShortcut
            ? "DevTools shortcut"
            : functionKeys.includes(event.key)
              ? `Function key "${event.key}"`
              : `Restricted key "${event.key}"`;
          await saveTermination({
            studentId,
            reason: violationType,
          });
          const updatedCount = await countUwhizAttemp(studentId);
          setViolationCount(updatedCount);
          if (updatedCount === 1) {
            setShowWarning(true);
            toast.warning(`${violationType} detected.`);
          } else if (updatedCount === 2) {
            setShowWarning(true);
            toast.warning(`${violationType} detected. One more violation will terminate the quiz.`);
          } else if (updatedCount >= 3) {
            setShowTermination(true);
            setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
            if (studentId) {
              saveExamAttempt(studentId);
            }
            toast.error("Quiz terminated due to multiple cheating attempts.");
          }
        } catch (error: unknown) {
          toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
        } finally {
          setIsApiCallPending(false);
        }
      }
    },
    [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]
  );

  const handleVisibilityChange = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;

    if (document.hidden) {
      setIsApiCallPending(true);
      try {
        await saveTermination({
          studentId,
          reason: "Tab switch",
        });
        if (!studentId) {
          setViolationCount(0);
          return;
        }
        const updatedCount = await countUwhizAttemp(studentId as string);
        setViolationCount(updatedCount);
        if (updatedCount === 1) {
          setShowWarning(true);
          toast.warning("Tab switch detected.");
        } else if (updatedCount === 2) {
          setShowWarning(true);
          toast.warning("Again tab switch detected. One more violation will terminate the quiz.");
        } else if (updatedCount >= 3) {
          setShowTermination(true);
          setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
          if (studentId) {
            saveExamAttempt(studentId);
          }
          toast.error("Quiz terminated due to multiple cheating attempts.");
        }
      } catch (error: unknown) {
        toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
      } finally {
        setIsApiCallPending(false);
      }
    }
  }, [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]);

  const handleContextMenu = useCallback(
    async (event: MouseEvent) => {
      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;
      event.preventDefault();
      toast.warning("Right-click is disabled during the quiz.");
    },
    [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]
  );

  const handleWindowBlur = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;

    setIsApiCallPending(true);
    try {
      await saveTermination({
        studentId,
        reason: "Window blur",
      });
      const updatedCount = await countUwhizAttemp(studentId as string);
      setViolationCount(updatedCount);
      if (updatedCount === 1) {
        setShowWarning(true);
        toast.warning("Window focus lost.");
      } else if (updatedCount == 2) {
        setShowWarning(true);
        toast.warning("Window focus lost again. One more violation will terminate the quiz.");
      } else if (updatedCount >= 3) {
        setShowTermination(true);
        setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
        if (studentId) {
          saveExamAttempt(studentId);
        }
        toast.error("Quiz terminated due to multiple cheating attempts.");
      }
    } catch (error: unknown) {
      toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
    } finally {
      setIsApiCallPending(false);
    }
  }, [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]);

  const handleFullScreenChange = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen || showWarning || isApiCallPending || isExamTakenDialogOpen) return;

    if (!document.fullscreenElement) {
      setIsApiCallPending(true);
      try {
        await saveTermination({
          studentId,
          reason: "Full-screen exit",
        });
        if (!studentId) {
          setViolationCount(0);
          return;
        }
        const updatedCount = await countUwhizAttemp(studentId as string);
        setViolationCount(updatedCount);
        if (updatedCount === 1) {
          setShowWarning(true);
          toast.warning("You have exited full-screen mode.");
        } else if (updatedCount === 2) {
          setShowWarning(true);
          toast.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.");
        } else if (updatedCount >= 3) {
          setShowTermination(true);
          setTerminationMessage("Quiz terminated due to multiple cheating attempts.");
          if (studentId) {
            saveExamAttempt(studentId);
          }
          toast.error("Quiz terminated due to multiple cheating attempts.");
        }
      } catch (error: unknown) {
        toast.error("Failed to save termination record.", { description: error instanceof Error ? error.message : "Unknown error" });
      } finally {
        setIsApiCallPending(false);
      }
    }
  }, [studentId, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen]);

  const handleGoHome = async () => {
    setShowTermination(false);

    const mobileRequest = localStorage.getItem('mobile_request');

    if (mobileRequest === 'true') {
      localStorage.removeItem('mobile_request');
      window.location.href = 'UEST://DailyQuiz';
    }

    const isFullScreen =
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement;

    if (isFullScreen) {
      const success = await exitFullScreen();
      if (success) {
        router.push("/mock-exam-card");
      } else {
        toast.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.");
        router.push("/mock-exam-card");
      }
    } else {
      router.push("/mock-exam-card");
    }

    setTimeout(() => {
      router.push("/mock-exam-card");
    }, 1000);
  };

  useEffect(() => {
    if (!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !showTermination && !isExamTakenDialogOpen) {
      document.addEventListener("visibilitychange", handleVisibilityChange);
      document.addEventListener("keydown", handleKeyDown);
      window.addEventListener("blur", handleWindowBlur);
      document.addEventListener("contextmenu", handleContextMenu);
      document.addEventListener("fullscreenchange", handleFullScreenChange);
    }
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      document.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("blur", handleWindowBlur);
      document.removeEventListener("contextmenu", handleContextMenu);
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, [
    handleVisibilityChange,
    handleKeyDown,
    handleWindowBlur,
    handleContextMenu,
    handleFullScreenChange,
    isDialogOpen,
    isLoginDialogOpen,
    isProfileDialogOpen,
    showTermination,
    isExamTakenDialogOpen,
  ]);

  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }, []);

  const progress = useMemo(() => {
    return questions.length > 0
      ? ((currentQuestionIndex + 1) / questions.length) * 100
      : 0;
  }, [currentQuestionIndex, questions]);

  const getButtonClass = (optionKey: string) => {
    const baseClass = `w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white`;

    if (selectedAnswer === optionKey) {
      return `${baseClass} bg-orange-100 border-orange-500`;
    }
    return baseClass;
  };

  const handleOptionClick = (optionKey: string) => {
    if (!showAnswerFeedback) {
      setSelectedAnswer(optionKey);
    }
  };

  if (isLoginDialogOpen) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
          <h2 className="text-lg sm:text-2xl font-bold mb-4">Login Required</h2>
          <p className="mb-4 text-sm sm:text-base text-gray-600">
            Please log in as a student to access the quiz.
          </p>
          <Button
            onClick={() => router.push(`/student/login?redirect=/mock-test${isWeekly ? '?isWeekly=true' : ''}`)}
            className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all"
          >
            Login to Continue
          </Button>
        </div>
      </div>
    );
  }

  if (isProfileDialogOpen) {
    const heading = isProfileNotApproved
      ? 'Profile Not Approved'
      : 'Complete Your Profile';

    const message = isProfileNotApproved
      ? 'Your profile has not been approved yet. Please wait for approval and check notifications.'
      : 'Your profile is incomplete. Please complete your profile to proceed.';

    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
          <h2 className="text-lg sm:text-2xl font-bold mb-4">{heading}</h2>
          <p className="mb-4 text-sm sm:text-base text-gray-600">{message}</p>
          <Button
            onClick={handleGoToProfile}
            className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all"
          >
            {isProfileNotApproved ? 'Update Profile' : 'Complete Profile'}
          </Button>
        </div>
      </div>
    );
  }

  if (isExamTakenDialogOpen) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
          <h2 className="text-lg sm:text-2xl font-bold mb-4 text-black">Exam Already Taken</h2>
          <p className="mb-4 text-sm sm:text-base text-gray-600">
            {isWeekly
              ? "You have already attempted the weekly exam this week. Please try again next Sunday."
              : "You have already attempted the daily exam today. Please try again tomorrow."}
          </p>
          <Button
            onClick={handleCloseExamTakenDialog}
            className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all"
          >
            Go to Home
          </Button>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <p className="text-base sm:text-xl font-medium mr-4">Loading questions...</p>
        <Loader className="w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange" />
      </div>
    );
  }

  const handleDownload = async () => {
    if (cardRef.current) {
      try {
        const { width } = cardRef.current.getBoundingClientRect();
        const scrollHeight = cardRef.current.scrollHeight;

        const dataUrl = await htmlToImage.toJpeg(cardRef.current, {
          quality: 1.0,
          pixelRatio: 2,
          backgroundColor: '#ffffff',
          canvasWidth: width,
          canvasHeight: scrollHeight,
          style: {
            margin: "0",
            padding: "35px 0px 0px 20px  ",
          },
        });

        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = isWeekly ? 'uest-weekly-quiz-result.jpg' : 'uest-daily-quiz-result.jpg';
        link.click();
      } catch (error) {
        console.error('Failed to download card:', error);
        toast.error('Failed to download the quiz result card. Please try again.');
      }
    }
  };

  if (isQuizCompleted) {
    const shareUrl = "https://uest.in/mock-exam-card";
    const shareText = `I scored ${calculateScore}/${questions.length} with a streak of ${streakData?.streak || 0} ${isWeekly ? 'weeks' : 'days'} on U-whiz ${isWeekly ? 'Weekly' : 'Daily'} Daily Exam! 🎉 Try it out!`;

    const studentData = localStorage.getItem('student_data');
    const parsedStudentData = studentData ? JSON.parse(studentData) : null;
    const studentName = parsedStudentData ? `${parsedStudentData.firstName} ${parsedStudentData.lastName}` : 'Unknown Student';

    return (
      <div className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
        <div className="relative z-10 w-full max-w-md text-center">
          <div ref={cardRef} className="bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-md">
            <div className="mb-6 text-center text-sm text-gray-600 dark:text-gray-400 flex flex-col items-center">
              <p>Powered by</p>
              <Image
                src="/logo.png"
                alt="Preply Logo"
                width={120}
                height={40}
              />
            </div>
            <div className="absolute inset-0 z-0 pointer-events-none">
              <div className="animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full" />
            </div>
            <h1 className="text-3xl font-extrabold text-customOrange dark:text-orange-400 mb-4 text-center">{isWeekly ? 'Weekly' : 'Daily'} Quiz Completed 🎉</h1>
            <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
              Congratulations, <span className="text-customOrange font-bold">{studentName}</span>
            </p>
            <p className="text-base text-gray-600 dark:text-gray-400 mb-4 text-center">
              Keep up the momentum. 💪
            </p>
            <div className="w-1/3 mx-auto h-2 bg-gray-200 dark:bg-gray-600 rounded-full mb-4">
              <div
                className="h-full bg-customOrange dark:bg-orange-400 rounded-full transition-all duration-500"
                style={{ width: `${questions.length > 0 ? (calculateScore / questions.length) * 100 : 0}%` }}
              ></div>
            </div>
            <p className="text-base text-gray-800 dark:text-gray-200 mb-4 text-center">
              Final Score: <span className="font-bold">{calculateScore}</span> / {questions.length}
            </p>
            <div className="flex justify-center mb-4">
              <div className="relative flex flex-col items-center">
                <div className="absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping" />
                <div className="absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse" />
                <div className="absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce" />
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning">
                  <span className="text-4xl">🔥</span>
                </div>
                <span className="mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400 text-center">
                  {streakLoading ? 'Loading Streak...' : streakError ? 'Error Loading Streak' : `🔥 Streak: ${streakData?.streak || 0} ${isWeekly ? 'Weeks' : 'Days'}`}
                </span>
              </div>
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-300 space-y-1 mb-6 text-center">
              <p><strong>Coins Earned:</strong> {calculateCoins + (streakData?.streak || 0)}</p>
              <p><strong>Bonus:</strong> +{streakData?.streak || 0} for Streak!</p>
            </div>
          </div>

          <div className="flex flex-col mt-6 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-xl">
            <div className="flex flex-col items-center justify-between gap-4">
              <p className="text-sm text-gray-800 dark:text-gray-300 flex-1">
                Ask your friends to join the quiz and get a chance to win some coins!
              </p>
              <div className="social-media-buttons flex items-center gap-4">
                <WhatsappShareButton url={shareUrl} title={shareText}>
                  <WhatsappIcon size={32} round />
                </WhatsappShareButton>
              </div>
            </div>
            <div className="flex mt-4 gap-4">
              <button
                onClick={handleDownload}
                className="download-button bg-orange-500 text-white px-6 py-3 rounded-lg shadow-md hover:bg-orange-600 min-w-[150px] whitespace-nowrap"
              >
                📷 Download Result
              </button>
              <button
                onClick={handleGoHome}
                className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold px-6 py-3 rounded-lg shadow-md min-w-[150px] whitespace-nowrap"
              >
                🚀 Continue Learning
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];

  return (
    <div className="flex flex-col min-h-screen bg-gray-100 text-gray-900">
      {isDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto">
            <h2 className="text-lg sm:text-2xl font-bold mb-4">Start {isWeekly ? 'Weekly' : 'Daily'} Quiz</h2>
            <p className="font-semibold mb-4 text-sm sm:text-base text-gray-600">
              Note: This is a mock exam for testing purposes only.
            </p>
            <div className="flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base">
              <p className="font-semibold mb-2">Instructions (English):</p>
              <ul className="list-disc list-inside mb-4 text-gray-600">
                <li>Do not switch tabs during the quiz.</li>
                <li>Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys).</li>
                <li>Do not open Developer Tools.</li>
                <li>Do not exit full-screen mode.</li>
                <li>Do not interact with other windows or applications.</li>
                <li>Do not change the screen or minimize the quiz window.</li>
                <li>Do not receive or make calls during the quiz.</li>
                <li>Do not use split screen or floating windows on your device.</li>
              </ul>
              <p className="font-semibold mb-2">સૂચનાઓ (ગુજરાતી):</p>
              <ul className="list-disc list-inside text-gray-600">
                <li>ક્વિઝ દરમિયાન ટેબ બદલશો નહીં.</li>
                <li>પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં.</li>
                <li>ડેવલપર ટૂલ્સ ખોલશો નહીં.</li>
                <li>ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં.</li>
                <li>ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં.</li>
                <li>અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં.</li>
                <li>સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં.</li>
                <li>ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં.</li>
                <li>તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં.</li>
              </ul>
            </div>
            <Button
              onClick={handleStartQuiz}
              className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all"
            >
              Start {isWeekly ? 'Weekly' : 'Daily'} Quiz
            </Button>
          </div>
        </div>
      )}

      {showWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-lg sm:text-2xl font-bold mb-4 text-customOrange">Warning</h2>
            <p className="mb-4 text-sm sm:text-base text-gray-600">
              You have performed a restricted action. Repeating this will terminate the quiz.
            </p>
            <Button
              onClick={() => setShowWarning(false)}
              className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all"
            >
              OK
            </Button>
          </div>
        </div>
      )}

      {showTermination && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-lg sm:text-2xl font-bold mb-4 text-red-500">Quiz Terminated</h2>
            <p className="mb-4 text-sm sm:text-base text-gray-600">
              {terminationMessage || "Your quiz has been terminated due to multiple cheating attempts."}
            </p>
            <Button
              onClick={handleGoHome}
              className="bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all"
            >
              Go to Home
            </Button>
          </div>
        </div>
      )}

      {!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !isExamTakenDialogOpen && (
        <>
          <QuizHeader />

          <div className="fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200">
            <div
              className="h-3.5 bg-customOrange rounded-r-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen">
            <div className="flex flex-col items-center justify-center w-full max-w-3xl">
              <div className="mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg">
                <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse" />
                <span className="text-lg sm:text-2xl font-bold text-customOrange">
                  {formatTime(timeLeft)}
                </span>
              </div>
              <div className="w-full text-center flex flex-col items-center">
                <div className="flex justify-center mb-3 sm:mb-4">
                  <span className="text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </span>
                </div>
                <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto">
                  <h2 className="text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6">
                    {currentQuestion.question}
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full">
                    <Button
                      variant="outline"
                      className={getButtonClass("optionOne")}
                      onClick={() => handleOptionClick("optionOne")}
                      disabled={showTermination || showAnswerFeedback}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        A
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionOne}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionTwo")}
                      onClick={() => handleOptionClick("optionTwo")}
                      disabled={showTermination || showAnswerFeedback}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        B
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionTwo}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionThree")}
                      onClick={() => handleOptionClick("optionThree")}
                      disabled={showTermination || showAnswerFeedback}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        C
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionThree}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionFour")}
                      onClick={() => handleOptionClick("optionFour")}
                      disabled={showTermination || showAnswerFeedback}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0">
                        D
                      </span>
                      <span className="flex-1 text-left whitespace-normal break-words">{currentQuestion.optionFour}</span>
                    </Button>
                  </div>
                </div>
                <Button
                  className="bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={() => handleNextQuestion()}
                  disabled={showTermination || showAnswerFeedback}
                >
                  {currentQuestionIndex === questions.length - 1 ? "Finish" : "Next Question"}
                </Button>
              </div>
              <footer className="fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm">
                <span>Powered by</span>
                <span className="font-semibold">UEST EdTech</span>
              </footer>
            </div>
          </div>
        </>
      )}
    </div>
  );
}